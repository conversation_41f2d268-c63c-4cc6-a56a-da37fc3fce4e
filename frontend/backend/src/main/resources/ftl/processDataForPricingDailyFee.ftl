${tc.signature("ast", "domainClass", "dailyFee")}
${cd4c.method("public GemScatterPlotData processDataForPricingDailyFee()")}

// Get data from the current page context
var pricings = this.getPricings();
List<GemScatterPlotData.DataPoint> dataPoints = new ArrayList<>();

// Process each instance to build scatter plot data
for (Pricing pricing : pricings) {
    // Numeric attribute - create scatter points
    Number dailyFee = pricing.getDailyFee();
    if (dailyFee != null) {
        // Use index as X coordinate and attribute value as Y coordinate
        double x = dataPoints.size();
        double y = dailyFee.doubleValue();
        dataPoints.add(new GemScatterPlotData.DataPoint(x, y));
    }
}

// Build scatter plot data
GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
for (GemScatterPlotData.DataPoint point : dataPoints) {
    builder.addDataPoint(point.getX(), point.getY());
}

return builder.build().get();
