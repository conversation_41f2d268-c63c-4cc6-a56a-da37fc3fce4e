${tc.signature("ast", "domainClass", "name")}
${cd4c.method("public GemPieChartData processDataForCustomerName()")}

// Get data from the current page context
var customers = this.getCustomers();
Map<String, Integer> distribution = new HashMap<>();

// Process each instance to build distribution map
for (Customer customer : customers) {
    // Enum attribute - group by enum values
    String name = customer.getName().toString();
    if (name != null) {
        distribution.put(name, distribution.getOrDefault(name, 0) + 1);
    }
}

// Build pie chart data
GemPieChartDataBuilder builder = new GemPieChartDataBuilder();
for (Map.Entry<String, Integer> entry : distribution.entrySet()) {
    builder.addEntry(entry.getKey(), entry.getValue());
}

return builder.build().get();

