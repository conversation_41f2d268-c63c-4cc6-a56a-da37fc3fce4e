${tc.signature("ast", "domainClass", "returnDate")}
${cd4c.method("public GemLineChartData processDataForRentalTransactionReturnDate()")}

// Get data from the current page context
var rentaltransactions = this.getRentalTransactions();
Map<String, List<Number>> timeSeriesData = new HashMap<>();

// Process each instance to build time series data
for (RentalTransaction rentaltransaction : rentaltransactions) {
<#if isDate>
    // Date attribute - group by time periods
    Date returnDate = rentaltransaction.getReturnDate();
    if (returnDate != null) {
        String timePeriod = formatDateForChart(returnDate);
        timeSeriesData.computeIfAbsent(timePeriod, k -> new ArrayList<>()).add(1.0);
    }
<#else>
    // Numeric attribute - time series analysis
    Number returnDate = rentaltransaction.getReturnDate();
    if (returnDate != null) {
        String timeKey = "Data Point " + timeSeriesData.size();
        timeSeriesData.computeIfAbsent(timeKey, k -> new ArrayList<>()).add(returnDate);
    }
}

// Build line chart data
GemLineChartDataBuilder builder = new GemLineChartDataBuilder();

// Convert to chart format
List<String> sortedKeys = new ArrayList<>(timeSeriesData.keySet());
Collections.sort(sortedKeys);

for (String key : sortedKeys) {
    List<Number> values = timeSeriesData.get(key);
    double average = values.stream().mapToDouble(Number::doubleValue).average().orElse(0.0);
    builder.addDataPoint(key, average);
}

return builder.build().get();

<#if isDate>
// Helper method for date formatting
private String formatDateForChart(Date date) {
    SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
    return formatter.format(date);
}
