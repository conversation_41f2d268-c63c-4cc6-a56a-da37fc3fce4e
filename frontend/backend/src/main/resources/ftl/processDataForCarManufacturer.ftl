${tc.signature("ast", "domainClass", "manufacturer")}
${cd4c.method("public GemPieChartData processDataForCarManufacturer()")}

// Get data from the current page context
var cars = this.getCars();
Map<String, Integer> distribution = new HashMap<>();

// Process each instance to build distribution map
for (Car car : cars) {
    // Enum attribute - group by enum values
    String manufacturer = car.getManufacturer().toString();
    if (manufacturer != null) {
        distribution.put(manufacturer, distribution.getOrDefault(manufacturer, 0) + 1);
    }
}

// Build pie chart data
GemPieChartDataBuilder builder = new GemPieChartDataBuilder();
for (Map.Entry<String, Integer> entry : distribution.entrySet()) {
    builder.addEntry(entry.getKey(), entry.getValue());
}

return builder.build().get();

