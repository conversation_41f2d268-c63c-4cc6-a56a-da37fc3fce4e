${tc.signature("ast", "domainClass", "mileage")}
${cd4c.method("public GemScatterPlotData processDataForCarMileage()")}

// Get data from the current page context
var cars = this.getCars();
List<GemScatterPlotData.DataPoint> dataPoints = new ArrayList<>();

// Process each instance to build scatter plot data
for (Car car : cars) {
    // Numeric attribute - create scatter points
    Number mileage = car.getMileage();
    if (mileage != null) {
        // Use index as X coordinate and attribute value as Y coordinate
        double x = dataPoints.size();
        double y = mileage.doubleValue();
        dataPoints.add(new GemScatterPlotData.DataPoint(x, y));
    }
}

// Build scatter plot data
GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
for (GemScatterPlotData.DataPoint point : dataPoints) {
    builder.addDataPoint(point.getX(), point.getY());
}

return builder.build().get();
