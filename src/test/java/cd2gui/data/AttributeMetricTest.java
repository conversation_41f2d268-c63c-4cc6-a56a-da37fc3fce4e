package data;

import cd2gui.data.*;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import mc.fenix.charts.gemlinecharttypes.GemLineChartData;
import mc.fenix.charts.gempiecharttypes.GemPieChartData;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;

/**
 * Test class for AttributeMetric.
 *
 */
public class AttributeMetricTest {

    @Test
    public void testBarChartMetric() {
        BarChartDetail chartDetail = new BarChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1, 2, 3));

        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>(
                "attrTest",
                ChartType.BAR_CHART,
                chartDetail
        );

        assertEquals(ChartType.BAR_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testLineChartMetric() {
        LineChartDetail chartDetail = new LineChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1.0, 2.0, 3.0));

        AttributeMetric<GemLineChartData> metric = new AttributeMetric<>(
                "attrTest",
                ChartType.LINE_CHART,
                chartDetail
        );

        assertEquals(ChartType.LINE_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testPieChartMetric() {
        PieChartDetail chartDetail = new PieChartDetail();
        chartDetail.addEntry("Entry", 100);

        AttributeMetric<GemPieChartData> metric = new AttributeMetric<>(
                "attrTest",
                ChartType.PIE_CHART,
                chartDetail
        );

        assertEquals(ChartType.PIE_CHART, metric.getType());
        assertTrue(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testNonVisualizableMetric() {
        BarChartDetail chartDetail = new BarChartDetail();

        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>(
                "attrTest",
                ChartType.NONE,
                chartDetail
        );

        assertEquals(ChartType.NONE, metric.getType());
        assertFalse(metric.isVisualizableAttribute());
        assertEquals(chartDetail, metric.getDetail());
    }

    @Test
    public void testToString() {
        BarChartDetail chartDetail = new BarChartDetail();
        AttributeMetric<GemBarChartData> metric = new AttributeMetric<>("attrTest", ChartType.BAR_CHART, chartDetail);

        String result = metric.toString();
        assertTrue(result.contains("AttributeMetric"));
        assertTrue(result.contains("BAR_CHART"));
        assertTrue(result.contains("isVisualizable=true"));
    }
}
