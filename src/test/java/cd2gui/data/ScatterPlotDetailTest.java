package data;

import cd2gui.data.ScatterPlotDetail;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotData;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotPoint;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotShape;
import mc.fenix.charts.gemscatterplottypes.GemScatterPlotAxis;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;
import java.util.Optional;

/**
 * Test class for ScatterPlotDetail.
 *
 */
public class ScatterPlotDetailTest {

    @Test
    public void testDefaultConstructor() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        assertNotNull(detail.getData());
        assertNotNull(detail.getXAxis());
        assertNotNull(detail.getYAxis());
    }

    @Test
    public void testCreatePoint() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        GemScatterPlotPoint point = detail.createPoint(1.5, 2.5, "Test Point", Optional.of(5));

        assertEquals(1.5, point.getX(), 0.001);
        assertEquals(2.5, point.getY(), 0.001);
        assertEquals("Test Point", point.getLabel());
        assertEquals(Optional.of(5), point.getPointRadius());
    }

    @Test
    public void testAddSet() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        GemScatterPlotPoint point1 = detail.createPoint(1.0, 2.0, "Point1", Optional.empty());
        GemScatterPlotPoint point2 = detail.createPoint(3.0, 4.0, "Point2", Optional.empty());

        detail.addSet("Test Set",
                Arrays.asList(point1, point2),
                Optional.of(GemScatterPlotShape.CIRCLE),
                Optional.of("red"),
                Optional.of("darkred"));

        GemScatterPlotData data = detail.getData();
        assertEquals(1, data.getSetsList().size());
        assertEquals("Test Set", data.getSetsList().get(0).getLabel());
        assertEquals(2, data.getSetsList().get(0).getPointsList().size());
    }

    @Test
    public void testSetData() {
        ScatterPlotDetail detail = new ScatterPlotDetail();
        GemScatterPlotData newData = new GemScatterPlotData();

        detail.setData(newData);

        assertEquals(newData, detail.getData());
    }

    @Test
    public void testAxisConfiguration() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        GemScatterPlotAxis xAxis = new GemScatterPlotAxis(
                Optional.of("X Axis"),
                Optional.of(0.0),
                Optional.of(10.0),
                Optional.of(1.0)
        );

        GemScatterPlotAxis yAxis = new GemScatterPlotAxis(
                Optional.of("Y Axis"),
                Optional.of(-5.0),
                Optional.of(5.0),
                Optional.of(0.5)
        );

        detail.setXAxis(xAxis);
        detail.setYAxis(yAxis);

        assertEquals(xAxis, detail.getXAxis());
        assertEquals(yAxis, detail.getYAxis());
        assertEquals(Optional.of("X Axis"), detail.getXAxis().getLabel());
        assertEquals(Optional.of("Y Axis"), detail.getYAxis().getLabel());
    }

    @Test
    public void testMultipleSets() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        GemScatterPlotPoint point1 = detail.createPoint(1.0, 1.0, "P1", Optional.empty());
        GemScatterPlotPoint point2 = detail.createPoint(2.0, 2.0, "P2", Optional.empty());
        GemScatterPlotPoint point3 = detail.createPoint(3.0, 3.0, "P3", Optional.empty());

        detail.addSet("Set1", Arrays.asList(point1), Optional.of(GemScatterPlotShape.CIRCLE), Optional.empty(), Optional.empty());
        detail.addSet("Set2", Arrays.asList(point2, point3), Optional.of(GemScatterPlotShape.SQUARE), Optional.empty(), Optional.empty());

        GemScatterPlotData data = detail.getData();
        assertEquals(2, data.getSetsList().size());
        assertEquals("Set1", data.getSetsList().get(0).getLabel());
        assertEquals("Set2", data.getSetsList().get(1).getLabel());
        assertEquals(1, data.getSetsList().get(0).getPointsList().size());
        assertEquals(2, data.getSetsList().get(1).getPointsList().size());
    }

    @Test
    public void testEmptySet() {
        ScatterPlotDetail detail = new ScatterPlotDetail();

        detail.addSet("Empty Set", Arrays.asList(), Optional.empty(), Optional.empty(), Optional.empty());

        GemScatterPlotData data = detail.getData();
        assertEquals(1, data.getSetsList().size()); // Empty sets are allowed by the builder
        assertEquals(0, data.getSetsList().get(0).getPointsList().size()); // But the points list should be empty
    }
}
