package data;

import cd2gui.data.BarChartDetail;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.Arrays;

/**
 * Test class for BarChartDetail.
 *
 */
public class BarChartDetailTest {

    @Test
    public void testDefaultConstructor() {
        BarChartDetail detail = new BarChartDetail();

        assertNotNull(detail.getData());
        assertFalse(detail.isStacked());
        assertEquals(100, detail.getMaxValue());
        assertEquals(0, detail.getMinValue());
    }

    @Test
    public void testAddEntry() {
        BarChartDetail detail = new BarChartDetail();
        detail.addEntry("Test Entry", Arrays.asList(1, 2, 3));

        GemBarChartData data = detail.getData();
        assertEquals(1, data.sizeEntries());
        assertEquals("Test Entry", data.getEntries(0).getLabel().orElse(""));
        assertEquals(3, data.getEntries(0).sizeData());
    }

    @Test
    public void testAddLabel() {
        BarChartDetail detail = new BarChartDetail();
        detail.addLabel("Label1");
        detail.addLabel("Label2");

        GemBarChartData data = detail.getData();
        assertEquals(2, data.sizeLabels());
        assertEquals("Label1", data.getLabels(0));
        assertEquals("Label2", data.getLabels(1));
    }

    @Test
    public void testSetData() {
        BarChartDetail detail = new BarChartDetail();
        GemBarChartData newData = new GemBarChartData();
        newData.addLabels("New Label");

        detail.setData(newData);

        assertEquals(newData, detail.getData());
        assertEquals(1, detail.getData().sizeLabels());
    }

    @Test
    public void testStackedProperty() {
        BarChartDetail detail = new BarChartDetail();

        assertFalse(detail.isStacked());

        detail.setStacked(true);
        assertTrue(detail.isStacked());

        detail.setStacked(false);
        assertFalse(detail.isStacked());
    }

    @Test
    public void testMaxMinValues() {
        BarChartDetail detail = new BarChartDetail();

        assertEquals(100, detail.getMaxValue());
        assertEquals(0, detail.getMinValue());

        detail.setMaxValue(200);
        detail.setMinValue(10);

        assertEquals(200, detail.getMaxValue());
        assertEquals(10, detail.getMinValue());
    }

    @Test
    public void testMultipleEntries() {
        BarChartDetail detail = new BarChartDetail();
        detail.addEntry("Entry1", Arrays.asList(1, 2));
        detail.addEntry("Entry2", Arrays.asList(3, 4));
        detail.addEntry("Entry3", Arrays.asList(5, 6));

        GemBarChartData data = detail.getData();
        assertEquals(3, data.sizeEntries());

        assertEquals("Entry1", data.getEntries(0).getLabel().orElse(""));
        assertEquals("Entry2", data.getEntries(1).getLabel().orElse(""));
        assertEquals("Entry3", data.getEntries(2).getLabel().orElse(""));
    }

    @Test
    public void testEmptyData() {
        BarChartDetail detail = new BarChartDetail();
        detail.addEntry("NonEmpty", Arrays.asList(1));

        GemBarChartData data = detail.getData();
        assertEquals(1, data.sizeEntries());
        assertEquals(1, data.getEntries(0).sizeData());
    }
}
