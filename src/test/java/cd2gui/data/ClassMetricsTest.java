package data;

import cd2gui.data.*;
import mc.fenix.charts.gembarcharttypes.GemBarChartData;
import org.junit.Test;
import static org.junit.Assert.*;

import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

/**
 * Test class for ClassMetrics.
 *
 */
public class ClassMetricsTest {

    private AttributeMetric<GemBarChartData> createVisualizableMetric(String name, double confidence) {
        BarChartDetail chartDetail = new BarChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1, 2, 3));
        return new AttributeMetric<>(name, ChartType.BAR_CHART, chartDetail);
    }

    private AttributeMetric<GemBarChartData> createNonVisualizableMetric(String name) {
        BarChartDetail chartDetail = new BarChartDetail();
        chartDetail.addLabel("Label");
        chartDetail.addEntry("Entry", Arrays.asList(1, 2, 3));
        return new AttributeMetric<>(name, ChartType.NONE, chartDetail);
    }

    @Test
    public void testEmptyMetrics() {
        ClassMetrics classMetrics = new ClassMetrics(new ArrayList<>());

        assertFalse(classMetrics.hasVisualizableAttributes());
        assertEquals(0, classMetrics.getAttributeMetrics().size());
        assertEquals(Integer.valueOf(0), classMetrics.getTotalAttributesInt());
    }

    @Test
    public void testNullMetrics() {
        ClassMetrics classMetrics = new ClassMetrics(null);

        assertFalse(classMetrics.hasVisualizableAttributes());
        assertEquals(0, classMetrics.getAttributeMetrics().size());
        assertEquals(Integer.valueOf(0), classMetrics.getTotalAttributesInt());
    }

    @Test
    public void testMixedMetrics() {
        BarChartDetail barDetail = new BarChartDetail();
        barDetail.addEntry("Entry", Arrays.asList(1, 2, 3));
        PieChartDetail pieDetail = new PieChartDetail();
        pieDetail.addEntry("Entry", 100);

        List<AttributeMetric<?>> metrics = Arrays.asList(
                new AttributeMetric<>("attrTest1", ChartType.BAR_CHART, barDetail),
                new AttributeMetric<>("attrTest2", ChartType.NONE, barDetail),
                new AttributeMetric<>("attrTest3", ChartType.PIE_CHART, pieDetail),
                new AttributeMetric<>("attrTest4", ChartType.NONE, barDetail)
        );

        ClassMetrics classMetrics = new ClassMetrics(metrics);

        assertTrue(classMetrics.hasVisualizableAttributes());
        assertEquals(2, classMetrics.getAttributeMetrics().size());
        assertEquals(Integer.valueOf(2), classMetrics.getTotalAttributesInt());

        List<AttributeMetric<?>> visualizable = classMetrics.getAttributeMetrics();
        assertEquals(ChartType.BAR_CHART, visualizable.get(0).getType());
        assertEquals(ChartType.PIE_CHART, visualizable.get(1).getType());
    }

    @Test
    public void testAllVisualizableMetrics() {
        List<AttributeMetric<?>> metrics = Arrays.asList(
                createVisualizableMetric("attr1", 0.7),
                createVisualizableMetric("attr2", 0.8),
                createVisualizableMetric("attr3", 0.9)
        );

        ClassMetrics classMetrics = new ClassMetrics(metrics);

        assertTrue(classMetrics.hasVisualizableAttributes());
        assertEquals(3, classMetrics.getAttributeMetrics().size());
        assertEquals(Integer.valueOf(3), classMetrics.getTotalAttributesInt());
    }

    @Test
    public void testAllNonVisualizableMetrics() {
        List<AttributeMetric<?>> metrics = Arrays.asList(
                createNonVisualizableMetric("attr1"),
                createNonVisualizableMetric("attr2")
        );

        ClassMetrics classMetrics = new ClassMetrics(metrics);

        assertFalse(classMetrics.hasVisualizableAttributes());
        assertEquals(0, classMetrics.getAttributeMetrics().size());
        assertEquals(Integer.valueOf(0), classMetrics.getTotalAttributesInt());
    }

    @Test
    public void testAverageConfidence() {
        List<AttributeMetric<?>> metrics = Arrays.asList(
                createVisualizableMetric("attr1", 0.6),
                createVisualizableMetric("attr2", 0.8),
                createVisualizableMetric("attr3", 1.0)
        );

        ClassMetrics classMetrics = new ClassMetrics(metrics);

        // Confidence calculation removed as per design requirements
    }

    @Test
    public void testDataImmutability() {
        List<AttributeMetric<?>> originalMetrics = new ArrayList<>(Arrays.asList(
                createVisualizableMetric("attr1", 0.8)
        ));

        ClassMetrics classMetrics = new ClassMetrics(originalMetrics);

        originalMetrics.add(createVisualizableMetric("attr2", 0.9));

        assertEquals(1, classMetrics.getAttributeMetrics().size());

        classMetrics.getAttributeMetrics().add(createVisualizableMetric("attr3", 0.7));

        assertEquals(1, classMetrics.getAttributeMetrics().size());
    }

    @Test
    public void testToString() {
        List<AttributeMetric<?>> metrics = Arrays.asList(
                createVisualizableMetric("attr1", 0.8),
                createNonVisualizableMetric("attr2")
        );

        ClassMetrics classMetrics = new ClassMetrics(metrics);
        String result = classMetrics.toString();

        assertTrue(result.contains("1 metrics"));
        assertTrue(result.contains("totalAttributesInt=1"));
        assertTrue(result.contains("hasVisualizableAttributes=true"));
    }
}
