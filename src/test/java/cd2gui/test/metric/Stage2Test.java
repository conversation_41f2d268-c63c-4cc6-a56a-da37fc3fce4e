package cd2gui.test.metric;

import cd2gui.data.*;
import cd2gui.metric.AttributeMetricIdentifier;
import cd2gui.metric.UnifiedAttributeAnalyzer;
import cd2gui.test.parser.AbstractTest;
import de.monticore.cdbasis._ast.ASTCDAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.cdbasis._ast.ASTCDCompilationUnit;
import org.junit.Before;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.*;

import static org.junit.Assert.*;

/**
 * Comprehensive Stage2 test suite. Tests AttributeMetricIdentifier and UnifiedAttributeAnalyzer
 * functionality including pattern recognition, measurement scale analysis, and empty chart specification generation.
 * Tests Stage2's core responsibility: analyzing attribute types and generating chart specifications for Stage3.
 *
 * <AUTHOR> Yang
 */
public class Stage2Test extends AbstractTest {

    private AttributeMetricIdentifier identifier;
    private UnifiedAttributeAnalyzer analyzer;
    private ASTCDCompilationUnit domainModel;

    /**
     * Sets up test environment with Stage2 components and domain model.
     *
     * <AUTHOR>
     */
    @Before
    public void setUp() throws IOException {
        identifier = new AttributeMetricIdentifier();
        analyzer = new UnifiedAttributeAnalyzer();
        domainModel = parseDomain(new File("src/test/resources/Domain.cd"));
    }

    /**
     * Tests AttributeMetricIdentifier initialization and basic functionality.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testAttributeMetricIdentifierInitialization() {
        assertNotNull("Identifier should be initialized", identifier);
        assertNotNull("Analyzer should be accessible", identifier.getAnalyzer());
        assertTrue("Analyzer should be UnifiedAttributeAnalyzer instance",
                identifier.getAnalyzer() instanceof UnifiedAttributeAnalyzer);
    }

    /**
     * Tests Stage2 processing with CD2GUIAttribute data from Stage1.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testStage2ProcessingWithSimulatedData() {
        ASTCDClass testClass = findClassInModel("Student");
        assertNotNull("Student class should exist in domain model", testClass);

        // Create CD2GUIAttribute list from Stage1 data
        List<CD2GUIAttribute> attributes = createSimulatedAttributes(testClass);

        // Test main Stage2 processing - should generate empty chart specifications
        List<AttributeMetric<?>> results = identifier.processClassForVisualization(testClass, attributes);
        assertNotNull("Results should not be null", results);

        // Verify all results are valid chart specifications
        for (AttributeMetric<?> result : results) {
            assertNotNull("Chart type should not be null", result.getType());
            assertNotNull("Chart detail should not be null", result.getDetail());
            assertTrue("Chart should be visualizable", result.isVisualizableAttribute());
        }

        // Test chart count
        int chartCount = identifier.getChartCount(testClass, attributes);
        assertEquals("Chart count should match results size", results.size(), chartCount);
    }

    /**
     * Tests Stage2 processing with null inputs.
     * Verifies robust error handling for invalid Stage1 data.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testStage2ProcessingNullInputs() {
        // Test null class
        List<AttributeMetric<?>> nullClassResult = identifier.processClassForVisualization(null, new ArrayList<>());
        assertEquals("Should return empty list for null class", 0, nullClassResult.size());

        // Test null attributes
        ASTCDClass testClass = findClassInModel("Student");
        List<AttributeMetric<?>> nullAttrsResult = identifier.processClassForVisualization(testClass, null);
        assertEquals("Should return empty list for null attributes", 0, nullAttrsResult.size());

        // Test chart count with null inputs
        assertEquals("Chart count should be 0 for null inputs", 0, identifier.getChartCount(null, null));
    }

    /**
     * Tests pattern recognition capabilities with domain model classes.
     * Verifies that Stage2 can detect business patterns and generate appropriate chart specifications.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testPatternRecognitionWithDomainModel() {
        // Test with Address class (geographic pattern potential)
        ASTCDClass addressClass = findClassInModel("Address");
        assertNotNull("Address class should exist", addressClass);

        List<CD2GUIAttribute> addressAttributes = createSimulatedAttributes(addressClass);
        List<AttributeMetric<?>> addressResults = identifier.processClassForVisualization(addressClass, addressAttributes);
        assertNotNull("Address results should not be null", addressResults);

        // Verify that results are empty chart specifications (no real data)
        for (AttributeMetric<?> result : addressResults) {
            assertNotNull("Chart type should not be null", result.getType());
            assertNotNull("Chart detail should not be null", result.getDetail());
        }

        // Test with Room class (numeric attributes)
        ASTCDClass roomClass = findClassInModel("Room");
        assertNotNull("Room class should exist", roomClass);

        List<CD2GUIAttribute> roomAttributes = createSimulatedAttributes(roomClass);
        List<AttributeMetric<?>> roomResults = identifier.processClassForVisualization(roomClass, roomAttributes);
        assertNotNull("Room results should not be null", roomResults);
    }

    /**
     * Tests that Stage2 generates empty chart.
     * This is the core Stage2 responsibility: analyze types and create chart templates for Stage3.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testEmptyChartSpecificationGeneration() {
        ASTCDClass studentClass = findClassInModel("Student");
        assertNotNull("Student class should exist", studentClass);

        List<CD2GUIAttribute> attributes = createSimulatedAttributes(studentClass);
        List<AttributeMetric<?>> results = identifier.processClassForVisualization(studentClass, attributes);

        for (AttributeMetric<?> result : results) {
            // Verify chart structure is created
            assertNotNull("Chart type should not be null", result.getType());
            assertNotNull("Chart detail should not be null", result.getDetail());

            // Verify chart is visualizable
            assertTrue("Chart should be visualizable", result.isVisualizableAttribute());

            // Verify chart type is one of the supported types
            ChartType chartType = result.getType();
            assertTrue("Chart type should be supported",
                    chartType == ChartType.PIE_CHART ||
                            chartType == ChartType.BAR_CHART ||
                            chartType == ChartType.LINE_CHART ||
                            chartType == ChartType.SCATTER_PLOT);
        }
    }

    /**
     * Tests MetricScale integration and supported scales functionality.
     *
     * <AUTHOR> Yang
     */
    @Test
    public void testMetricScaleIntegration() {
        List<MetricScale> supportedScales = MetricScale.getSupportedScales();

        assertNotNull("MetricScale.getSupportedScales() should not be null", supportedScales);
        assertEquals("Should support exactly 4 scales", 4, supportedScales.size());

        for (MetricScale scale : supportedScales) {
            assertTrue("All supported scales should be metric suitable",
                    scale.isMetricSuitable());
        }
    }

    /**
     * Finds a class by name in the domain model.
     */
    private ASTCDClass findClassInModel(String className) {
        return domainModel.getCDDefinition().getCDClassesList().stream()
                .filter(clazz -> clazz.getName().equals(className))
                .findFirst()
                .orElse(null);
    }

    /**
     * Tests scale determination logic with different attribute types.
     */
    @Test
    public void testScaleDeterminationLogic() {
        ASTCDClass studentClass = findClassInModel("Student");
        assertNotNull("Student class should exist", studentClass);

        // Test that processing doesn't crash with different attribute types
        for (ASTCDAttribute attribute : studentClass.getCDAttributeList()) {
            String attrName = attribute.getName();
            // Just verify that attribute processing doesn't throw exceptions
            assertNotNull("Attribute name should not be null", attrName);
        }
    }

    /**
     * Tests Stage2 integration with multiple classes from domain model.
     */
    @Test
    public void testMultipleClassProcessing() {
        String[] testClasses = {"Student", "Lecturer", "Address", "Room", "Event"};

        for (String className : testClasses) {
            ASTCDClass clazz = findClassInModel(className);
            if (clazz != null) {
                List<CD2GUIAttribute> attributes = createSimulatedAttributes(clazz);

                // Test processing doesn't crash
                List<AttributeMetric<?>> results = identifier.processClassForVisualization(clazz, attributes);
                assertNotNull("Results should not be null for " + className, results);

                // Test chart count consistency
                int chartCount = identifier.getChartCount(clazz, attributes);
                assertEquals("Chart count should match results size for " + className,
                        results.size(), chartCount);
            }
        }
    }

    /**
     * Tests enum handling with Grade enum from domain model.
     */
    @Test
    public void testEnumHandling() {
        ASTCDClass thesisClass = findClassInModel("Thesis");
        if (thesisClass != null) {
            List<CD2GUIAttribute> attributes = createSimulatedAttributes(thesisClass);

            // Thesis class has Grade enum attribute
            List<AttributeMetric<?>> results = identifier.processClassForVisualization(thesisClass, attributes);
            assertNotNull("Thesis results should not be null", results);

            // Test that enum attributes can be processed
            for (AttributeMetric<?> result : results) {
                assertNotNull("Chart type should not be null", result.getType());
                assertTrue("Chart type should be visualization suitable",
                        result.getType().isVisualizationSuitable());
            }
        }
    }

    /**
     * Tests performance with larger attribute sets.
     */
    @Test
    public void testPerformanceWithLargerSets() {
        ASTCDClass testClass = findClassInModel("OtherAttributes");
        if (testClass != null) {
            List<CD2GUIAttribute> attributes = createSimulatedAttributes(testClass);

            long startTime = System.currentTimeMillis();
            List<AttributeMetric<?>> results = identifier.processClassForVisualization(testClass, attributes);
            long endTime = System.currentTimeMillis();

            assertNotNull("Results should not be null", results);
            assertTrue("Processing should complete within reasonable time (< 1000ms)",
                    (endTime - startTime) < 1000);
        }
    }

    /**
     * Tests Stage2 output format compliance.
     */
    @Test
    public void testOutputFormatCompliance() {
        ASTCDClass studentClass = findClassInModel("Student");
        if (studentClass != null) {
            List<CD2GUIAttribute> attributes = createSimulatedAttributes(studentClass);
            List<AttributeMetric<?>> results = identifier.processClassForVisualization(studentClass, attributes);

            for (AttributeMetric<?> result : results) {
                // Test AttributeMetric structure
                assertNotNull("Chart type should not be null", result.getType());
                assertNotNull("Chart detail should not be null", result.getDetail());

                // Test that chart type is one of the supported types
                ChartType chartType = result.getType();
                assertTrue("Chart type should be one of the core types",
                        chartType == ChartType.PIE_CHART ||
                                chartType == ChartType.BAR_CHART ||
                                chartType == ChartType.LINE_CHART ||
                                chartType == ChartType.SCATTER_PLOT);
            }
        }
    }

    /**
     * Creates CD2GUIAttribute list from ASTCDClass attributes.
     * Simulates Stage1 output that would be passed to Stage2.
     *
     * @param clazz UML class from domain model
     * @return CD2GUIAttribute list for Stage2 processing
     * <AUTHOR> Yang
     */
    private List<CD2GUIAttribute> createSimulatedAttributes(ASTCDClass clazz) {
        List<CD2GUIAttribute> attributes = new ArrayList<>();

        // Create CD2GUIAttribute for each ASTCDAttribute (simulating Stage1 output)
        for (ASTCDAttribute astAttribute : clazz.getCDAttributeList()) {
            CD2GUIAttribute guiAttribute = new CD2GUIAttribute(astAttribute);
            attributes.add(guiAttribute);
        }

        return attributes;
    }
}
