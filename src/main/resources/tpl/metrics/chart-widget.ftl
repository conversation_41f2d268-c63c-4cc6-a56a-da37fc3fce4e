<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "name", "attributeMetric")}

<#assign chartContent>
<#if attributeMetric??>
  <#assign chartType = attributeMetric.getType()>
  <#assign methodName = "processDataFor" + name + attributeMetric.getAttributeName()?cap_first>

  <#-- Use FTLGenerator generated templates instead of chart type switching -->
  <#switch chartType>
    <#case "PIE_CHART">
      @GemPieChart(data = ${methodName}())
      <#break>

    <#case "BAR_CHART">
      @GemBarChart(data = ${methodName}())
      <#break>

    <#case "LINE_CHART">
      @GemLineChart(data = ${methodName}())
      <#break>

    <#case "SCATTER_PLOT">
      @GemScatterPlot(data = ${methodName}())
      <#break>

    <#default>
      @GemText(value = "Chart type ${chartType} not supported")
  </#switch>
<#else>
  <#-- Debug: Show AttributeMetric status -->
  @GemText(
    value = "DEBUG: <#if attributeMetric??>Attr: ${attributeMetric.getAttributeName()}, Type: ${attributeMetric.getType()}, Visualizable: ${attributeMetric.isVisualizableAttribute()}<#else>AttributeMetric is null</#if>"
  )
</#if>
</#assign>





<#if attributeMetric??>
${name?uncap_first}_${attributeMetric.getAttributeName()}ChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "${attributeMetric.getAttributeName()} Analysis",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      ${chartContent}
    ]
  )
)
<#else>
${name?uncap_first}_NoDataChartCard@GemCard(
  width = "auto",
  height = "auto",
  title = "No Data Available",
  component = @GemColumn(
    hAlign = "center",
    vAlign = "center",
    components = [
      @GemText(
        value = "No metric data available for this attribute"
      )
    ]
  )
)
</#if>
