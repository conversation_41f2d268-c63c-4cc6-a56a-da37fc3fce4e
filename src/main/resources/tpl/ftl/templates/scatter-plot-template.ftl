<#-- <PERSON>atter plot data processing method template -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();
List<GemScatterPlotData.DataPoint> dataPoints = new ArrayList<>();

// Process each instance to build scatter plot data
for (${className} ${className?lower_case} : ${className?lower_case}s) {
<#if isNumeric>
    // Numeric attribute - create scatter points
    Number ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        // Use index as X coordinate and attribute value as Y coordinate
        double x = dataPoints.size();
        double y = ${attributeName}.doubleValue();
        dataPoints.add(new GemScatterPlotData.DataPoint(x, y));
    }
<#else>
    // Non-numeric attribute - convert to numeric representation
    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());
    if (${attributeName} != null) {
        double x = dataPoints.size();
        double y = ${attributeName}.hashCode() % 100; // Simple numeric conversion
        dataPoints.add(new GemScatterPlotData.DataPoint(x, y));
    }
</#if>
}

// Build scatter plot data
GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
for (GemScatterPlotData.DataPoint point : dataPoints) {
    builder.addDataPoint(point.getX(), point.getY());
}

return builder.build().get();
