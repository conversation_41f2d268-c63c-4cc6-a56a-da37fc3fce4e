<#-- Pie chart data processing method template -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();
Map<String, Integer> distribution = new HashMap<>();

// Process each instance to build distribution map
for (${className} ${className?lower_case} : ${className?lower_case}s) {
<#if isNumeric>
    // Numeric attribute - group by value ranges
    Number ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        String range = getValueRange(${attributeName});
        distribution.put(range, distribution.getOrDefault(range, 0) + 1);
    }
<#elseif isEnum>
    // Enum attribute - group by enum values
    String ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}().toString();
    if (${attributeName} != null) {
        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);
    }
<#else>
    // String or other attribute - group by string value
    String ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);
    }
</#if>
}

// Build pie chart data
GemPieChartDataBuilder builder = new GemPieChartDataBuilder();
for (Map.Entry<String, Integer> entry : distribution.entrySet()) {
    builder.addEntry(entry.getKey(), entry.getValue());
}

return builder.build().get();

<#if isNumeric>
// Helper method for numeric value ranges
private String getValueRange(Number value) {
    double val = value.doubleValue();
    if (val < 10) return "0-9";
    else if (val < 50) return "10-49";
    else if (val < 100) return "50-99";
    else return "100+";
}
</#if>
