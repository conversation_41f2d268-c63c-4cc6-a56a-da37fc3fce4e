<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "className", "attributeName", "methodName", "chartType", "metric", "domainPackage")}

<#-- Data processing method template for chart data generation -->
<#-- This template generates methods that process attribute data for chart visualization -->

${cd4c.method("public ${getReturnType(chartType)} ${methodName}()")}

<#-- Generate method implementation based on chart type -->
<#switch chartType>
  <#case "PIE_CHART">
    ${tc.includeArgs("tpl.ftl.methods.pie-chart-method", [domainClass, className, attributeName, methodName, metric])}
    <#break>
  
  <#case "BAR_CHART">
    ${tc.includeArgs("tpl.ftl.methods.bar-chart-method", [domainClass, className, attributeName, methodName, metric])}
    <#break>
  
  <#case "LINE_CHART">
    ${tc.includeArgs("tpl.ftl.methods.line-chart-method", [domainClass, className, attributeName, methodName, metric])}
    <#break>
  
  <#case "SCATTER_PLOT">
    ${tc.includeArgs("tpl.ftl.methods.scatter-plot-method", [domainClass, className, attributeName, methodName, metric])}
    <#break>
  
  <#default>
    // Default implementation for ${chartType}
    return null;
</#switch>

<#-- Helper function to determine return type based on chart type -->
<#function getReturnType chartType>
  <#switch chartType>
    <#case "PIE_CHART">
      <#return "GemPieChartData">
    <#case "BAR_CHART">
      <#return "GemBarChartData">
    <#case "LINE_CHART">
      <#return "GemLineChartData">
    <#case "SCATTER_PLOT">
      <#return "GemScatterPlotData">
    <#default>
      <#return "Object">
  </#switch>
</#function>
