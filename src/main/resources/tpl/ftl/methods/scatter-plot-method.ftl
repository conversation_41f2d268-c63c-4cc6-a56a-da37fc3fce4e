<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "className", "attributeName", "methodName", "chartType", "metric", "domainPackage")}

<#-- Scatter plot data processing method implementation -->
${cd4c.method("public GemScatterPlotData ${methodName}()")}

<#-- Method implementation -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();
List<GemScatterPlotPoint> points = new ArrayList<>();

<#if metric.isNumeric()>
// Numeric attribute - create scatter plot with index vs value
int index = 0;
for (${className} ${className?lower_case} : ${className?lower_case}s) {
    ${getAttributeType()} ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        GemScatterPlotPoint point = new GemScatterPlotPoint(
            (double) index,                    // x-coordinate (index)
            ${attributeName}.doubleValue(),    // y-coordinate (value)
            "${className?lower_case} " + index, // label
            Optional.empty()                   // color (use default)
        );
        points.add(point);
        index++;
    }
}

<#else>
// Non-numeric attribute - create scatter plot with frequency distribution
Map<String, Integer> frequency = new HashMap<>();
Map<String, Integer> indexMap = new HashMap<>();

// Count frequencies and assign indices
int index = 0;
for (${className} ${className?lower_case} : ${className?lower_case}s) {
    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());
    if (${attributeName} != null && !${attributeName}.equals("null")) {
        frequency.put(${attributeName}, frequency.getOrDefault(${attributeName}, 0) + 1);
        if (!indexMap.containsKey(${attributeName})) {
            indexMap.put(${attributeName}, index++);
        }
    }
}

// Create scatter plot points
for (Map.Entry<String, Integer> entry : frequency.entrySet()) {
    String value = entry.getKey();
    Integer count = entry.getValue();
    Integer valueIndex = indexMap.get(value);
    
    GemScatterPlotPoint point = new GemScatterPlotPoint(
        (double) valueIndex,  // x-coordinate (category index)
        (double) count,       // y-coordinate (frequency)
        value,                // label
        Optional.empty()      // color (use default)
    );
    points.add(point);
}
</#if>

// Create scatter plot data set
GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()
    .label("${attributeName?cap_first} Distribution")
    .points(points)
    .color(Optional.of("blue"))
    .build().get();

// Build scatter plot data
GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();
GemScatterPlotData scatterPlotData = builder.build().get();
scatterPlotData.addSets(dataSet);

return scatterPlotData;

<#-- Helper function to get attribute type -->
<#function getAttributeType>
    <#if metric.isNumeric()>
        <#return "Number">
    <#else>
        <#return "String">
    </#if>
</#function>
