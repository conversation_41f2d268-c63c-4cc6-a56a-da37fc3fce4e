<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "className", "attributeName", "methodName", "metric")}

<#-- Line chart data processing method implementation -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();

<#if metric.isNumeric()>
// Numeric attribute - create time series or ordered data
List<GemLineChartEntry> entries = new ArrayList<>();
List<Double> dataPoints = new ArrayList<>();

// Extract numeric values and sort them
for (${className} ${className?lower_case} : ${className?lower_case}s) {
    ${getAttributeType()} ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        dataPoints.add(${attributeName}.doubleValue());
    }
}

// Sort data points for line chart
Collections.sort(dataPoints);

// Create line chart entry
GemLineChartEntry entry = new GemLineChartEntryBuilder()
    .label("${attributeName?cap_first} Values")
    .data(dataPoints)
    .build().get();
entries.add(entry);

<#else>
// Non-numeric attribute - create frequency over index
List<GemLineChartEntry> entries = new ArrayList<>();
Map<String, Integer> frequency = new HashMap<>();
List<Double> dataPoints = new ArrayList<>();

// Count frequencies
for (${className} ${className?lower_case} : ${className?lower_case}s) {
    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());
    if (${attributeName} != null && !${attributeName}.equals("null")) {
        frequency.put(${attributeName}, frequency.getOrDefault(${attributeName}, 0) + 1);
    }
}

// Convert to data points (index-based)
int index = 0;
for (Map.Entry<String, Integer> entry : frequency.entrySet()) {
    dataPoints.add((double) entry.getValue());
    index++;
}

// Create line chart entry
GemLineChartEntry entry = new GemLineChartEntryBuilder()
    .label("${attributeName?cap_first} Frequency")
    .data(dataPoints)
    .build().get();
entries.add(entry);
</#if>

// Build line chart data
GemLineChartDataBuilder builder = new GemLineChartDataBuilder();
builder.entries(entries);

return builder.build().get();

<#-- Helper function to get attribute type -->
<#function getAttributeType>
    <#if metric.isNumeric()>
        <#return "Number">
    <#else>
        <#return "String">
    </#if>
</#function>
