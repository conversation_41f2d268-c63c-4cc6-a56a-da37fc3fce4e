<#-- (c) https://github.com/MontiCore/monticore -->
${tc.signature("domainClass", "className", "attributeName", "methodName", "metric")}

<#-- Bar chart data processing method implementation -->
// Get data from the current page context
var ${className?lower_case}s = this.get${className}s();
Map<String, Integer> distribution = new HashMap<>();

// Process each instance to build distribution map
for (${className} ${className?lower_case} : ${className?lower_case}s) {
    <#-- Handle different attribute types -->
    <#if metric.isNumeric()>
    // Numeric attribute - group by value ranges
    ${getAttributeType()} ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}();
    if (${attributeName} != null) {
        String range = getValueRange(${attributeName});
        distribution.put(range, distribution.getOrDefault(range, 0) + 1);
    }
    <#elseif metric.isEnum()>
    // Enum attribute - group by enum values
    String ${attributeName} = ${className?lower_case}.get${attributeName?cap_first}().toString();
    if (${attributeName} != null) {
        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);
    }
    <#else>
    // String or other attribute - group by string value
    String ${attributeName} = String.valueOf(${className?lower_case}.get${attributeName?cap_first}());
    if (${attributeName} != null && !${attributeName}.equals("null")) {
        distribution.put(${attributeName}, distribution.getOrDefault(${attributeName}, 0) + 1);
    }
    </#if>
}

// Build bar chart data
GemBarChartDataBuilder builder = new GemBarChartDataBuilder();

// Add labels and data
List<String> labels = new ArrayList<>(distribution.keySet());
Collections.sort(labels); // Sort for consistent display

for (String label : labels) {
    builder.addLabel(label);
    builder.addEntry(label, Arrays.asList(distribution.get(label)));
}

return builder.build().get();

<#-- Helper method for numeric value ranges -->
<#if metric.isNumeric()>
private String getValueRange(${getAttributeType()} value) {
    // Simple range grouping - can be customized based on data distribution
    if (value < 10) return "0-9";
    else if (value < 50) return "10-49";
    else if (value < 100) return "50-99";
    else return "100+";
}
</#if>

<#-- Helper function to get attribute type -->
<#function getAttributeType>
    <#if metric.isNumeric()>
        <#return "Number">
    <#else>
        <#return "String">
    </#if>
</#function>
