package cd2gui.ftl;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.generating.GeneratorEngine;
import de.monticore.generating.GeneratorSetup;
import de.monticore.generating.templateengine.GlobalExtensionManagement;
import de.monticore.io.paths.MCPath;
import de.se_rwth.commons.Names;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.nio.file.Path;
import java.util.*;

/**
 * Generates FTL templates for UMLP-Tool based on existing AttributeMetric objects.
 * Integrates with the existing CD2GUI template generation system using GeneratorEngine.
 *
 * This class creates data processing method templates that are referenced by the
 * chart components in the metrics visualization system.
 */
public class FTLGenerator {

    private final GeneratorEngine generator;
    private final String domainPackage;
    private final String domainName;
    private final static Set<String> generatedTemplates = new HashSet<>();

    /**
     * Creates a new FTLGenerator instance integrated with the CD2GUI template system.
     *
     * @param targetFilepath Output directory for generated templates
     * @param hwcPath Handwritten code path (for template extensions)
     * @param domainPackage Domain package name for proper imports
     * @param domainName Domain name for file organization
     */
    public FTLGenerator(File targetFilepath, MCPath hwcPath, String domainPackage, String domainName) {
        this.domainPackage = domainPackage;
        this.domainName = domainName;

        // Setup generator using the same pattern as GuiModelFileCreator
        GeneratorSetup setup = new GeneratorSetup();
        setup.setOutputDirectory(targetFilepath);
        setup.setTracing(false);

        // Add global extensions for consistency with CD2GUI
        GlobalExtensionManagement glex = new GlobalExtensionManagement();
        glex.setGlobalValue("domainPackage", domainPackage);
        glex.setGlobalValue("domainName", domainName);
        setup.setGlex(glex);

        this.generator = new GeneratorEngine(setup);
    }

    /**
     * Generates FTL templates for all metrics in the provided map.
     * Uses the existing template generation infrastructure for consistency.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public final void generateFTLTemplates(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            return;
        }

        // Clear previous generation tracking
        generatedTemplates.clear();

        Log.info("Starting FTL template generation for " + metricsMap.size() + " classes", "FTLGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            ASTCDClass clazz = entry.getKey();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                generateTemplatesForClass(clazz, metrics);
                Log.info("Generated " + metrics.size() + " FTL templates for class " + clazz.getName(), "FTLGenerator");
            }
        }

        Log.info("FTL template generation completed", "FTLGenerator");
    }

    /**
     * Generates all templates for a specific class and its metrics.
     * Groups metrics by chart type for more efficient generation.
     *
     * @param clazz The class containing the attributes
     * @param metrics List of metrics for this class
     */
    private void generateTemplatesForClass(ASTCDClass clazz, List<AttributeMetric<?>> metrics) {
        String className = clazz.getName();

        // Group metrics by chart type to avoid duplicate template generation
        Map<ChartType, List<AttributeMetric<?>>> metricsByType = new HashMap<>();
        for (AttributeMetric<?> metric : metrics) {
            ChartType chartType = metric.getType();
            if (chartType != null && chartType != ChartType.NONE) {
                metricsByType.computeIfAbsent(chartType, k -> new ArrayList<>()).add(metric);
            }
        }

        // Generate templates for each chart type
        for (Map.Entry<ChartType, List<AttributeMetric<?>>> entry : metricsByType.entrySet()) {
            ChartType chartType = entry.getKey();
            List<AttributeMetric<?>> typeMetrics = entry.getValue();

            for (AttributeMetric<?> metric : typeMetrics) {
                generateTemplateForMetric(clazz, metric, chartType);
            }
        }
    }

    /**
     * Generates FTL template for a specific metric using the template engine.
     * Uses existing template infrastructure instead of StringBuilder concatenation.
     *
     * @param clazz The class containing the attribute
     * @param metric AttributeMetric object containing chart information
     * @param chartType The chart type for this metric
     */
    private void generateTemplateForMetric(ASTCDClass clazz, AttributeMetric<?> metric, ChartType chartType) {
        if (metric == null || metric.getAttributeName() == null) {
            return;
        }

        String className = clazz.getName();
        String attributeName = metric.getAttributeName();
        String methodName = generateMethodName(chartType, className, attributeName);

        // Check for duplicates
        if (generatedTemplates.contains(methodName)) {
            Log.debug("Skipping duplicate FTL template: " + methodName, "FTLGenerator");
            return;
        }

        try {
            generateDataMethodTemplate(clazz, metric, chartType, methodName);
            generatedTemplates.add(methodName);
            Log.info("Generated FTL template: " + methodName + ".ftl", "FTLGenerator");
        } catch (Exception e) {
            Log.error("Failed to generate FTL template for " + methodName + ": " + e.getMessage(), "FTLGenerator");
        }
    }

    /**
     * Generates the method name for a chart data processing method.
     * Follows the naming convention used in the chart templates.
     *
     * @param chartType Type of chart
     * @param className Name of the class
     * @param attributeName Name of the attribute
     * @return Generated method name
     */
    private String generateMethodName(ChartType chartType, String className, String attributeName) {
        String prefix = getMethodPrefix(chartType);
        return prefix + className + capitalize(attributeName);
    }

    /**
     * Gets the method prefix based on chart type.
     * Matches the naming convention used in existing chart templates.
     */
    private String getMethodPrefix(ChartType chartType) {
        switch (chartType) {
            case PIE_CHART:
                return "processDataFor";
            case BAR_CHART:
                return "getBarChartData4";
            case LINE_CHART:
                return "getLineChartData4";
            case SCATTER_PLOT:
                return "processDataFor";
            default:
                return "processDataFor";
        }
    }

    /**
     * Generates a data processing method template using the template engine.
     * This replaces the StringBuilder approach with proper template generation.
     *
     * @param clazz The class containing the attribute
     * @param metric The metric information
     * @param chartType The type of chart
     * @param methodName The generated method name
     */
    private void generateDataMethodTemplate(ASTCDClass clazz, AttributeMetric<?> metric,
                                          ChartType chartType, String methodName) {
        String className = clazz.getName();
        String attributeName = metric.getAttributeName();

        // Create template path following CD2GUI conventions
        Path templatePath = Path.of("ftl", "data-methods", methodName + ".ftl");

        // Use the generator engine to create the template
        generator.generate("tpl/ftl/data-method.ftl",
                templatePath,
                clazz,
                // Template arguments
                clazz,                    // domainClass
                className,                // className
                attributeName,            // attributeName
                methodName,               // methodName
                chartType,                // chartType
                metric,                   // metric
                domainPackage            // domainPackage
        );
    }

    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }

    /**
     * Creates a factory method for FTLGenerator instances.
     * Integrates with the existing CD2GUI architecture.
     *
     * @param targetFilepath Output directory
     * @param hwcPath Handwritten code path
     * @param domainPackage Domain package name
     * @param domainName Domain name
     * @return Configured FTLGenerator instance
     */
    public static FTLGenerator create(File targetFilepath, MCPath hwcPath,
                                    String domainPackage, String domainName) {
        return new FTLGenerator(targetFilepath, hwcPath, domainPackage, domainName);
    }
}
