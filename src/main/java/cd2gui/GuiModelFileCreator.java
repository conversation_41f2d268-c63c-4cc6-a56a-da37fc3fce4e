/* (c) https://github.com/MontiCore/monticore */
package cd2gui;

import cd2gui.data.*;
import cd2gui.metric.AttributeMetricIdentifier;
import cd2gui.util.ASTCDClassManager;
import cd2gui.util.AttributeManager;
import cd2gui.util.RoleManager;
import cd2gui.util.Types;
import de.monticore.cdassociation._symboltable.CDRoleSymbol;
import de.monticore.cdbasis._ast.ASTCDAttribute;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.monticore.generating.GeneratorEngine;
import de.monticore.generating.GeneratorSetup;
import de.monticore.generating.templateengine.reporting.Reporting;
import de.monticore.io.FileReaderWriter;
import de.monticore.io.paths.MCPath;
import de.se_rwth.commons.Joiners;
import de.se_rwth.commons.Names;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.nio.file.Path;
import java.util.*;
import java.util.stream.Collectors;

public class GuiModelFileCreator {

    private final List<ASTCDClass> classes;
    private final List<CD2GUIClassTreeNode> classTrees;
    private final GeneratorEngine generator;

    private final Integer numberOfAttributesDisplayed = 8;

    private final Map<ASTCDClass, List<CD2GUIAttribute>> attributeMap = new HashMap<>();

    private final Map<ASTCDClass, List<CD2GUIAssociation>> roleMap = new HashMap<>();

    private final Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = new HashMap<>();

    protected String domainPackage;

    protected String domainName;

    protected Path targetFilePath;

    /**
     * Creates a GuiModel file creator used in CD2GUI to generate all .gui files as well as the auxiliary file guiCreateModels.txt.
     * guiCreateModels is used to pass to the gui-Generator which models are used to create objects and which operate on existing objects.
     *
     * @param classes        a List of all ASTCDClasses that are used to generate the .gui files,
     *                       note that the symbols have to be added to the classes in order for the Generation to work
     * @param classTrees     a List of the classTrees of the classes Parameter, it contains trees of the class hierarchy
     *                       and is used to create links in the overview page to subclasses.
     *                       Can be generated by the ASTCDClassManager.buildSubclassTrees Method. For further Information look there or in CD2GUIClassTreeNode.
     * @param setup          The Setup that contains all parameters for the generator
     * @param domainPackage  The name of the Domain Package, this is needed to correctly import the Data Classes in the .gui models.
     */
    public GuiModelFileCreator(List<ASTCDClass> classes, List<CD2GUIClassTreeNode> classTrees, GeneratorSetup setup,
                               String domainName, String domainPackage) {
        this.classes = classes;
        this.classTrees = classTrees;
        this.targetFilePath = setup.getOutputDirectory().toPath();
        this.domainPackage = domainPackage;
        this.domainName = domainName;

        generator = new GeneratorEngine(setup);

        initializeAttributesAndRoles();
        initializeMetrics();
    }

    /**
     * Feature for extending pages of metrics data
     * It includes 3 pages, 2 new pages for metrics only (details and dashboard), another 1 for extending overview pages.
     * Edited: GuiModelFileCreator(), createOverviewPages()
     * New: createMetricsPage(), createMetricVisualizationPage(), createVisualizationDashboard(), initializeMetrics()
     * <AUTHOR> Ma
     */

    /**
     * Creates an overview page for each ASTCDClass, the resulting .gui model can be found in targetFilePath/models.
     * The Overview Page Model is a table that shows all instances of the particular class with the respective attributes.
     * The name is classname + Overview.gui. Overview Pages do not generate Objects and are therefore not found in guiCreateModels.txt.
     * The Template used is overview-gui.ftl.
     */
    public void createOverviewPages(){

        for (ASTCDClass clazz : classes) {
            List<ASTCDClass> subclasses = ASTCDClassManager.getTreeNode(classTrees, clazz).getSubclasses();
            subclasses.remove(clazz);
            subclasses = subclasses.stream().filter(ASTCDClassManager::hasPage).collect(Collectors.toList());

            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);
            
            //Ignore Enum Assocs for now
            attributes = attributes.stream().filter(CD2GUIAttribute::isVisibleInOverview).filter(a -> a.isAttribute() || a.isClass()).collect(Collectors.toList());
            attributes.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            //Add metrics data to overview pages
            List<AttributeMetric<?>> attributeMetrics = metricsMap.get(clazz);
            ClassMetrics classMetrics = new ClassMetrics(attributeMetrics);

            //display all keyAttributes, fill with attributes then Role Attributes up to numberOfAttributesDisplayed
            List<CD2GUIAttribute> trimmedAttributes = attributes.stream().filter(CD2GUIAttribute::isKeyAttribute).collect(Collectors.toList());
            if(trimmedAttributes.size() < numberOfAttributesDisplayed){
              attributes.removeIf(CD2GUIAttribute::isKeyAttribute);
              trimmedAttributes.addAll(attributes.stream().limit(numberOfAttributesDisplayed-trimmedAttributes.size()).collect(Collectors.toList()));
            }

            if (!ASTCDClassManager.isSingleton(clazz)) { // Don't generate overview pages for singletons
              Log.info("Generate overview page gui file for " + clazz.getName(), "CD2GUITool");
              Path filePath = Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()), domainName.toLowerCase(), clazz.getName() + "Overview.gui");
              generator.generate("tpl/overview-gui.ftl",
                  filePath,
                  clazz,
                  //Arguments
                  clazz,   //domainClass
                  clazz.getName(),           //name
                  domainPackage, //package name for data Classes
                  trimmedAttributes, //displayed Attributes in Overview
                  subclasses,
                  classMetrics
              );
              Reporting.reportFileCreation("overview-gui.ftl", filePath, clazz);
            }
        }
    }

    /**
     * Creates a details page for each ASTCDClass, the resulting .gui model can be found in targetFilePath/models.
     * The Details Page Model shows all Attributes of a specific instance of a class as well as all associations of that instance.
     * The name is classname + Details.gui. Details Pages do not generate Objects and are therefore not found in guiCreateModels.txt.
     * The Template used is details-gui.ftl.
     */
    public void createDetailsPages(){

        for (ASTCDClass clazz : classes) {
            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);
            attributes = attributes.stream().filter(CD2GUIAttribute::isVisibleInDetails).collect(Collectors.toList());
            attributes.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            List<CD2GUIAttribute> singleAssociations = attributeMap.get(clazz);
            singleAssociations = singleAssociations.stream().filter(CD2GUIAttribute::isRole).collect(Collectors.toList());
            singleAssociations.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            List<CD2GUIAssociation> roles = roleMap.get(clazz);
            roles = roles.stream().filter(CD2GUIAssociation::isVisibleInDetails).collect(Collectors.toList());
            roles.sort(new CD2GUIAssociation.SortCD2GUIAssociation());

            Log.info("Generate details page gui file for " + clazz.getName(), "CD2GUITool");
            Path filePath = Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()), domainName.toLowerCase(), clazz.getName() + "Details.gui");
            generator.generate("tpl/details-gui.ftl",
                    filePath,
                    clazz,
                    //Arguments
                    clazz,   //domainClass
                    clazz.getName(),          //name
                    domainPackage, //package name for data Classes
                    attributes,
                    roles,
                    singleAssociations
            );
            Reporting.reportFileCreation("details-gui.ftl", filePath, clazz);
        }
    }

    /**
     * Creates a details page for each ASTCDClass where the attributes and associations are editable,
     * the resulting .gui model can be found in targetFilePath/models.
     * The DetailsEdit Page Model is a Details Page Model, but the attributes and associations are editable.
     * The name is classname + DetailsEdit.gui. DetailsEdit Pages do not generate Objects and are therefore not found in guiCreateModels.txt.
     * The Template used is details-edit-gui.ftl.
     */
    public void createDetailsEditPages(){

        for (ASTCDClass clazz : classes) {
            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);
            attributes = attributes.stream().filter(CD2GUIAttribute::isVisibleInDetails).collect(Collectors.toList());
            attributes.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            List<CD2GUIAttribute> singleAssociations = attributeMap.get(clazz);
            singleAssociations = singleAssociations.stream().filter(CD2GUIAttribute::isRole).collect(Collectors.toList());
            singleAssociations.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            List<CD2GUIAssociation> roles = roleMap.get(clazz);
            roles = roles.stream().filter(CD2GUIAssociation::isVisibleInDetails).collect(Collectors.toList());
            roles.sort(new CD2GUIAssociation.SortCD2GUIAssociation());

            Log.info("Generate details edit page gui file for " + clazz.getName(), "CD2GUITool");
            Path filePath = Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()), domainName.toLowerCase(), clazz.getName() + "DetailsEdit.gui");
            generator.generate("tpl/details-edit-gui.ftl",
                    filePath,
                    clazz,
                    //Arguments
                    clazz,   //domainClass
                    clazz.getName(),          //name
                    domainPackage, //package name for data Classes
                    attributes,
                    roles,
                    singleAssociations
            );
            Reporting.reportFileCreation("details-gui.ftl", filePath, clazz);
        }
    }

    /**
     * Creates a form page for each ASTCDClass, the resulting .gui model can be found in targetFilePath/models.
     * The Form Page Model allows the creation of new instances of the respective class.
     * The name is classname + Form.gui. From Pages do generate Objects and are therefore found in guiCreateModels.txt.
     * The Template used is form-gui.ftl.
     */
    public void createFormPages(List<String> excludedForms){
        StringBuilder gUICreateBuilder = new StringBuilder();

        for (ASTCDClass clazz : classes) {
            String fullName = clazz.getSymbol().getFullName();
            if(excludedForms.contains(fullName)){
              Log.info("Skipping Form page gui file for " + clazz.getName() + " from exclude list", "CD2GUITool");
              continue;
            }

            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);
            attributes.sort(new CD2GUIAttribute.SortCD2GUIAttributes());

            List<CD2GUIAssociation> roles = roleMap.get(clazz);
            roles = roles.stream().filter(CD2GUIAssociation::isVisibleInDetails).collect(Collectors.toList());
            roles.sort(new CD2GUIAssociation.SortCD2GUIAssociation());

            Log.info("Generate Form page gui file for " + clazz.getName(), "CD2GUITool");
            Path filePath = Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()), domainName.toLowerCase(), clazz.getName() + "Form.gui");
            generator.generate("tpl/form-gui.ftl",
                    filePath,
                    clazz,
                    //Arguments
                    clazz,   //domainClass
                    clazz.getName(),          //name
                    domainPackage, //package name for data Classes
                    attributes,
                    roles
            );
            Reporting.reportFileCreation("form-gui.ftl", filePath, clazz);
            gUICreateBuilder.append(domainPackage.toLowerCase()).append(".").
                    append(clazz.getName()).append("Form\n");
        }
        String metaFile = Joiners.DOT.join("meta", domainPackage.toLowerCase(),  "guiCreateTextModels.txt");
        FileReaderWriter.storeInFile(targetFilePath.resolve(Names.getPathFromQualifiedName(metaFile)), gUICreateBuilder.toString());
    }

    /**
     * Creates the metrics page for CD2GUI, the resulting .gui model can be found in targetFilePath/models.
     * The Metrics Model contains different chart types and details according to different metrics.
     * Note: FTL templates for data processing methods are now generated in CD2GUITool.java
     */
    public void createMetricsPage(){
        // Generate the metrics GUI pages
        for (ASTCDClass clazz : classes) {
            if (metricsMap.containsKey(clazz)) {
                List<AttributeMetric<?>> attributeMetrics = metricsMap.get(clazz);

                ClassMetrics classMetrics = new ClassMetrics(attributeMetrics);

                Log.info("Generate metric visualization page for " + clazz.getName(), "CD2GUITool");
                Path filePath = Path.of(Names.getPathFromQualifiedName(domainPackage.toLowerCase()),
                        domainName.toLowerCase(), clazz.getName() + "Metric.gui");

                generator.generate("tpl/metrics-gui.ftl",
                        filePath,
                        clazz,
                        clazz,
                        clazz.getName(),
                        domainPackage,
                        classMetrics
                );
                Reporting.reportFileCreation("metrics-gui.ftl", filePath, clazz);
            }
        }
    }

    /**
     * Creates the dashboard page for CD2GUI, the resulting .gui model can be found in targetFilePath/models.
     * The Dashboard Model contains a link to each Overview page for easier navigation.
     * The name is CD2GUIDashboard.gui.The Dashboard does not generate Objects and is therefore not found in guiCreateModels.txt.
     * The Template used is dashboard-gui.ftl.
     */
    public void createDashboard(){
        Log.info("Generate the CD2GUI Dashboard", "CD2GUITool");
        Path filePath = Path.of(domainName.toLowerCase(),"CD2GUIDashboard.gui");
        generator.generateNoA("tpl/dashboard-gui.ftl",
                filePath,
                classes,
                domainPackage
        );
        Reporting.reportFileCreation(targetFilePath.resolve(filePath).toAbsolutePath().toString());
    }

    /**
     * Creates the attribute and role map for later use in the page generation.
     * This saves computation time, as both have to be calculated for most pages.
     * Both Attributes and Roles are wrapped, as Attributes and Roles can show up as Attributes or Associations in the generated page
     * ->[1] and -> [0..1] Roles are both displayed as Attributes.
     * List Attributes are displayed as Associations.
     */
    private void initializeAttributesAndRoles(){
        for(ASTCDClass clazz : classes){
            Set<ASTCDAttribute> attributes = AttributeManager.filterInvisibleAttributeList(
                    AttributeManager.getAllAttributes(clazz, true)
            );
            Set<CDRoleSymbol> roles = RoleManager.filterInvisibleRoleList(
                            RoleManager.getAllRoles(clazz)
            );

            List<CD2GUIAttribute> cd2GUIAttributes = new LinkedList<>();

            List<CD2GUIAssociation> cd2GUIAssociations = new LinkedList<>();

            for (ASTCDAttribute attribute : attributes){
                if(Types.isList(attribute.getSymbol().getType())){
                    cd2GUIAssociations.add(new CD2GUIAssociation(attribute));
                }
                else {
                    cd2GUIAttributes.add(new CD2GUIAttribute(attribute));
                }
            }
            for (CDRoleSymbol role : roles){
                if(RoleManager.isSingle(role)){
                    cd2GUIAttributes.add(new CD2GUIAttribute(role));
                }
                else{
                    cd2GUIAssociations.add(new CD2GUIAssociation(role));
                }
            }
            attributeMap.put(clazz, cd2GUIAttributes);
            roleMap.put(clazz, cd2GUIAssociations);
        }
    }

    /**
     * Initializes metrics map with AttributeMetric objects.
     * Fixed: Added null checking to prevent CD2GUI0x200 error in pie-chart.ftl.
     * Calls AttributeMetricIdentifier.processClassForVisualization().
     */
    public void initializeMetrics() {
        for (ASTCDClass clazz : classes) {
            //analyze metric data with class and attributes, include recommended chart types and details.
            AttributeMetricIdentifier identifier = new AttributeMetricIdentifier();
            List<CD2GUIAttribute> attributes = attributeMap.get(clazz);

            if (attributes == null) {
                Log.warn("No attributes found for class " + clazz.getName());
                continue;
            }

            List<AttributeMetric<?>> metrics = identifier.processClassForVisualization(clazz, attributes);

            if (metrics != null && !metrics.isEmpty()) {
                metricsMap.put(clazz, metrics);
                Log.info("Successfully added " + metrics.size() + " metrics for class " + clazz.getName(), "cd2gui");
            } else {
                Log.warn("No metrics generated for class " + clazz.getName());
            }
        }
    }

    /**
     * Gets the metrics map containing AttributeMetric objects for each class.
     * Used by FTLGenerator to access metric data for template generation.
     *
     * @return Map of class to list of AttributeMetric objects
     */
    public Map<ASTCDClass, List<AttributeMetric<?>>> getMetricsMap() {
        return metricsMap;
    }
}
